/**
 * @file UIEventHandler.cpp
 * @brief Centralized user interface event handling for the PicoMudrasSequencer.
 *
 * This module manages button interactions, mode switching, and parameter
 * control. It provides clean separation of UI logic from audio and sequencer
 * cores.
 */
#include "UIEventHandler.h"
#include "../midi/MidiManager.h"
#include "../sequencer/Sequencer.h"
#include "ButtonManager.h"
#include "../sensors/as5600.h"
#include "../sequencer/ShuffleTemplates.h"

// External function declarations that the UI calls
extern void onClockStart();
extern void onClockStop();
extern void setLEDTheme(LEDTheme theme);

// Correct extern for uClock:

// External variables that are still needed from the main file
extern uint8_t currentScale;
extern bool isClockRunning;
extern const ParameterDefinition CORE_PARAMETERS[];

// Helper function declarations (static to this file)
static bool handleParameterButtonEvent(const MatrixButtonEvent &evt,
                                       UIState &uiState);
static bool handleStepButtonEvent(const MatrixButtonEvent &evt,
                                  UIState &uiState, Sequencer &seq1,
                                  Sequencer &seq2);
static void handleLFOAssignment(uint8_t buttonIndex, UIState &uiState,
                                Sequencer &seq1, Sequencer &seq2);
static void autoSelectAS5600Parameter(ParamId paramId, UIState &uiState);
static void handleAS5600ParameterControl(UIState &uiState);
static void handleControlButtonEvent(uint8_t buttonIndex, UIState &uiState,
                                     Sequencer &seq1, Sequencer &seq2);

void initUIEventHandler(UIState &uiState) { initButtonManager(uiState); }

/**
 * @brief Handles events from the matrix button grid, updating UI state,
 * sequencers, and MIDI note manager as needed.
 *
 * This function processes button events from the matrix, supporting multiple
 * modes:
 * - LFO assignment mode: Assigns LFOs to parameters when active.
 * - Voice switch (button 24): Handles both short and long presses to switch
 * voices or enter LFO assignment mode.
 * - Parameter and step buttons: Delegates to specialized handlers.
 * - Control buttons: Handles control actions on button press.
 *
 * @param evt The matrix button event containing button index and event type
 * (pressed/released).
 * @param uiState Reference to the UI state object, which tracks current mode
 * and selections.
 * @param seq1 Reference to the first sequencer instance.
 * @param seq2 Reference to the second sequencer instance.
 * @param midiNoteManager Reference to the MIDI note manager for handling mode
 * switches.
 */
void matrixEventHandler(const MatrixButtonEvent &evt, UIState &uiState,
                        Sequencer &seq1, Sequencer &seq2,
                        MidiNoteManager &midiNoteManager) {
  // Handle LFO assignment mode
  if (uiState.lfoAssignMode) {
    if (evt.type == MATRIX_BUTTON_PRESSED) {
      handleLFOAssignment(evt.buttonIndex, uiState, seq1, seq2);
      uiState.lfoAssignMode = false;
      Serial.println("Exited LFO assignment mode");
    }
    return; // Exit after handling
  }

  // Handle Voice Switch (Button 24) with long press for LFO mode
  if (evt.buttonIndex == BUTTON_VOICE_SWITCH) {
    if (evt.type == MATRIX_BUTTON_PRESSED) {
      uiState.button24PressTime = millis();         //  start timer for hold functionality
      uiState.button24WasPressed = true;
    } else if (evt.type == MATRIX_BUTTON_RELEASED &&
               uiState.button24WasPressed) {
      unsigned long pressDuration = millis() - uiState.button24PressTime;   //  get the time held
      uiState.button24WasPressed = false;

      if (isLongPress(pressDuration)) {     //  if it was a long press
        uiState.lfoAssignMode = true;
        Serial.print("Entered LFO assignment mode for ");
        Serial.println(uiState.isVoice2Mode ? "LFO2 (Voice 2)"
                                            : "LFO1 (Voice 1)");
      } else {
        midiNoteManager.onModeSwitch();
        uiState.isVoice2Mode = !uiState.isVoice2Mode;     //  not a long press toggle instead
        uiState.selectedStepForEdit = -1;
        Serial.print("Switched to Voice ");
        Serial.println(uiState.isVoice2Mode ? "2" : "1");
      }
    }
    return; // Exit after handling
  }

  // Handle Randomization buttons (30, 31) as hold-down actions
  if (evt.buttonIndex == BUTTON_RANDOMIZE_SEQ1) {
    if (evt.type == MATRIX_BUTTON_PRESSED) {
      uiState.randSeq1ButtPressTime = millis();      //  start timer for long press functionality 
      uiState.randomizeSeq1ButtonHeld = true;
    } else if (evt.type == MATRIX_BUTTON_RELEASED &&
               uiState.randomizeSeq1ButtonHeld) {
      unsigned long pressDuration = millis() - uiState.randSeq1ButtPressTime;
      uiState.randomizeSeq1ButtonHeld = false;

      if (isLongPress(pressDuration)) {
          seq1.resetAllSteps();
        Serial.println("Randomized Sequencer 1 parameters");
      } else {
        seq1.randomizeParameters();
        Serial.println("Randomized Sequencer 1 parameters");
      }

      return; // Exit after handling
    }
  }
  if (evt.buttonIndex == BUTTON_RANDOMIZE_SEQ2) {
    if (evt.type == MATRIX_BUTTON_PRESSED) {
      uiState.randSeq2ButtPressTime = millis();
      uiState.randomizeSeq2ButtonHeld = true;
    } else if (evt.type == MATRIX_BUTTON_RELEASED &&
               uiState.randomizeSeq2ButtonHeld) {
      unsigned long pressDuration = millis() - uiState.randSeq2ButtPressTime;
      uiState.randomizeSeq2ButtonHeld = false;

      if (isLongPress(pressDuration)) {
        seq2.resetAllSteps();
        Serial.println("Reset Sequencer 2 parameters");
      } else {
        seq2.randomizeParameters();
        Serial.println("Randomized Sequencer 2 parameters");
      }
    }
    return; // Exit after handling
  }

  // Handle other buttons
  if (handleParameterButtonEvent(evt, uiState))
    return;
  if (handleStepButtonEvent(evt, uiState, seq1, seq2))
    return;

  // Handle control buttons (only on press)
  if (evt.type == MATRIX_BUTTON_PRESSED) {
    handleControlButtonEvent(evt.buttonIndex, uiState, seq1, seq2);
  }
}
// =======================
//   INTERNAL HANDLERS
// =======================

/**
 * @brief HANDLES PARAMETER BUTTON EVENTS FROM THE MATRIX BUTTON GRID.
 *
 * This function checks if the given button event corresponds to any
 * parameter button mapping. If a match is found, it updates the UI
 * state to reflect whether the parameter button is held, prints debug
 * information to the serial output, and, if the button is pressed and
 * not the "Note" parameter, selects the AS5600 for the corresponding
 * parameter.
 *
 * @param evt      The MatrixButtonEvent containing the button index and
 * event type (pressed/released).
 * @param uiState  Reference to the UIState object to update the held
 * state of parameter buttons.
 * @return true if the event was handled as a parameter button event;
 * false otherwise.
 */
static bool handleParameterButtonEvent(const MatrixButtonEvent &evt,
                                       UIState &uiState) {
  for (size_t i = 0; i < PARAM_BUTTON_MAPPINGS_SIZE; ++i) {
    const auto &mapping = PARAM_BUTTON_MAPPINGS[i];
    if (evt.buttonIndex == mapping.buttonIndex) {
      bool pressed = (evt.type == MATRIX_BUTTON_PRESSED);
      uiState.parameterButtonHeld[static_cast<int>(mapping.paramId)] = pressed;

      Serial.print("Button ");
      Serial.print(mapping.buttonIndex);
      Serial.print(" (");
      Serial.print(mapping.name);
      Serial.print(") ");
      Serial.println(pressed ? "pressed" : "released");

      // Automatically select AS5600 parameter if not the Note parameter and
      // button is pressed
      if (pressed && (mapping.paramId != ParamId::Note)) {
        autoSelectAS5600Parameter(mapping.paramId, uiState);
      }
      return true;
    }
  }
  return false;
}

static bool handleStepButtonEvent(const MatrixButtonEvent &evt,
                                  UIState &uiState, Sequencer &seq1,
                                  Sequencer &seq2) {
    // Ignore out-of-bounds button indices
    if (evt.buttonIndex >= NUMBER_OF_STEP_BUTTONS) {
      return false;
    }

    // Select current active sequencer based on voice mode
    Sequencer &currentActiveSeq = uiState.isVoice2Mode ? seq2 : seq1;

    // --- Slide mode: toggle slide for this step ---
    if (uiState.slideMode && evt.type == MATRIX_BUTTON_PRESSED) {
      uint8_t currentSlideValue = currentActiveSeq.getStepParameterValue(
          ParamId::Slide, evt.buttonIndex);
      uint8_t newSlideValue = (currentSlideValue > 0) ? 0 : 1;
      currentActiveSeq.setStepParameterValue(ParamId::Slide, evt.buttonIndex,
                                             newSlideValue);
      Serial.print("Step ");
      Serial.print(evt.buttonIndex);
      Serial.print(" slide ");
      Serial.println(newSlideValue > 0 ? "ON" : "OFF");
      return true;
    }

    // --- If holding any parameter button, pressing a step button will
    // adjust length of corresponding parameter ---
    if (isAnyParameterButtonHeld(uiState) &&
        evt.type == MATRIX_BUTTON_PRESSED) {
      const ParamButtonMapping *heldMapping = getHeldParameterButton(uiState);
      if (heldMapping) {
        uint8_t newStepCount = evt.buttonIndex + 1;
        currentActiveSeq.setParameterStepCount(heldMapping->paramId,
                                               newStepCount);
        Serial.print("Set ");
        Serial.print(heldMapping->name);
        Serial.print(" parameter length to ");
        Serial.println(newStepCount);
      }
      return true;
    }

    // --- Normal mode: differentiate between short/long press for
    // edit/select/toggle ---
    if (!isAnyParameterButtonHeld(uiState)) {
      if (evt.type == MATRIX_BUTTON_PRESSED) {
        // Record press time for this pad
        uiState.padPressTimestamps[evt.buttonIndex] = millis();
      } else if (evt.type == MATRIX_BUTTON_RELEASED) {
        unsigned long pressDuration =
            millis() - uiState.padPressTimestamps[evt.buttonIndex];
        uiState.padPressTimestamps[evt.buttonIndex] = 0;

        if (isLongPress(pressDuration)) {
          // Toggle step selection for editing
          uiState.selectedStepForEdit =
              (uiState.selectedStepForEdit == evt.buttonIndex)
                  ? -1
                  : evt.buttonIndex;
        } else {
          // Short press toggles step (on/off) and exits edit mode
          (uiState.isVoice2Mode ? seq2 : seq1).toggleStep(evt.buttonIndex);
          uiState.selectedStepForEdit = -1;
        }
      }
    }
    return true;
}

static void handleControlButtonEvent(uint8_t buttonIndex, UIState &uiState,
                                     Sequencer &seq1, Sequencer &seq2) {
  switch (buttonIndex) {
  case BUTTON_SLIDE_MODE:
    uiState.slideMode = !uiState.slideMode;
    uiState.selectedStepForEdit = -1;
    Serial.print("Slide mode ");
    Serial.println(uiState.slideMode ? "ON" : "OFF");
    break;
  case BUTTON_AS5600_CONTROL:
    handleAS5600ParameterControl(uiState);
    break;
  case BUTTON_PLAY_STOP:
    if (isClockRunning) {

      onClockStop();
      seq1.stop();
      seq2.stop();
    }

    else {
      onClockStart();
    }

    uiState.flash25Until = millis() + CONTROL_LED_FLASH_DURATION_MS;
    break;
  case BUTTON_CHANGE_SCALE:
    currentScale = (currentScale + 1) % 7;
    break;
  case BUTTON_CHANGE_THEME:
    uiState.currentThemeIndex =
        (uiState.currentThemeIndex + 1) % static_cast<int>(LEDTheme::COUNT);
    setLEDTheme(static_cast<LEDTheme>(uiState.currentThemeIndex));
    break;
  case BUTTON_RESET_SEQUENCERS:
    seq1.resetAllSteps();
    seq2.resetAllSteps();
    uiState.resetStepsLightsFlag = true;
    break;
  // BUTTON_RANDOMIZE_SEQ1 and BUTTON_RANDOMIZE_SEQ2 are now handled as
  // hold-down actions in the main event handler, not as toggle buttons
  // here
  case BUTTON_TOGGLE_DELAY:
    uiState.delayOn = !uiState.delayOn;
    uiState.flash23Until = millis() + CONTROL_LED_FLASH_DURATION_MS;
    if (uiState.delayOn) {
      uiState.currentAS5600Parameter = AS5600ParameterMode::DelayTime;
      Serial.println("Delay ON - AS5600 set to Delay Time");
    } else {
      Serial.println("Delay OFF");
    }
    break;
  }
}

static void handleLFOAssignment(uint8_t buttonIndex, UIState &uiState,
                                Sequencer &seq1, Sequencer &seq2) {
  Sequencer &currentActiveSeq = uiState.isVoice2Mode ? seq2 : seq1;
  ParamId paramId = ParamId::Count;

  for (size_t i = 0; i < PARAM_BUTTON_MAPPINGS_SIZE; ++i) {
    if (PARAM_BUTTON_MAPPINGS[i].buttonIndex == buttonIndex) {
      paramId = PARAM_BUTTON_MAPPINGS[i].paramId;
      break;
    }
  }

  if (paramId != ParamId::Count) {
    uint8_t lfoNum = uiState.isVoice2Mode ? 2 : 1;
    currentActiveSeq.assignLFO(lfoNum, paramId);
    Serial.print("Assigned LFO");
    Serial.print(lfoNum);
    Serial.print(" to ");
    Serial.println(CORE_PARAMETERS[static_cast<int>(paramId)].name);
  }
}

static void autoSelectAS5600Parameter(ParamId paramId, UIState &uiState) {
  AS5600ParameterMode newAS5600Param;
  bool isValid = true;
  switch (paramId) {
  case ParamId::Velocity:
    newAS5600Param = AS5600ParameterMode::Velocity;
    break;
  case ParamId::Filter:
    newAS5600Param = AS5600ParameterMode::Filter;
    break;
  case ParamId::Attack:
    newAS5600Param = AS5600ParameterMode::Attack;
    break;
  case ParamId::Decay:
    newAS5600Param = AS5600ParameterMode::Decay;
    break;
  default:
    isValid = false;
    break;
  }

  if (isValid && newAS5600Param != uiState.currentAS5600Parameter) {
    uiState.currentAS5600Parameter = newAS5600Param;
    Serial.print("AS5600 auto-selected: ");
    Serial.println(CORE_PARAMETERS[static_cast<int>(paramId)].name);
  }
}

static void handleAS5600ParameterControl(UIState &uiState) {
  // AS5600 Shuffle Template Mode:
  // If shuffle mode is active, interpret control as shuffle template selection.
  if (uiState.as5600ShuffleModeActive) {
    // Store the selected shuffle template index in UIState for main loop to apply
    // This follows the same pattern as other AS5600 parameters
    uiState.selectedShuffleTemplateIndex = getAS5600TemplateIndex(as5600Sensor, NUM_SHUFFLE_TEMPLATES);
    uiState.shuffleTemplateChanged = true; // Flag for main loop to apply the change
    Serial.print("AS5600 Shuffle template selected: ");
    Serial.println(uiState.selectedShuffleTemplateIndex);
    return;
  }

  // Long-press detection: Track press start and measure duration on (assumed) button release
  unsigned long now = millis();

  // If the press start time is not set, this is likely the button-down event: set start time.
  if (uiState.as5600ButtonPressStartTime == 0) {
    uiState.as5600ButtonPressStartTime = now;
    // Do not process parameter switching on press down.
    return;
  }

  // If press start time is set, this is likely the button-up/release event: check duration.
  unsigned long pressDuration = now - uiState.as5600ButtonPressStartTime;
  uiState.as5600ButtonPressStartTime = 0; // Reset for next press

  if (pressDuration >= 500) {
    // Long press toggles shuffle mode
    uiState.as5600ShuffleModeActive = !uiState.as5600ShuffleModeActive;
    if (uiState.as5600ShuffleModeActive) {
      Serial.println("Shuffle mode enabled");
    } else {
      Serial.println("Shuffle mode disabled");
    }
    uiState.lastAS5600ButtonPress = now;
  } else {
    Serial.println("Short press detected");
    // Short press: cycle parameter mode as before
    uiState.currentAS5600Parameter = static_cast<AS5600ParameterMode>(
        (static_cast<uint8_t>(uiState.currentAS5600Parameter) + 1) %
        static_cast<uint8_t>(AS5600ParameterMode::COUNT));
    Serial.print("AS5600 mode switched to: ");
    switch (uiState.currentAS5600Parameter) {
      case AS5600ParameterMode::Velocity:       Serial.println("Velocity"); break;
      case AS5600ParameterMode::Filter:         Serial.println("Filter"); break;
      case AS5600ParameterMode::Attack:         Serial.println("Attack"); break;
      case AS5600ParameterMode::Decay:          Serial.println("Decay"); break;
      case AS5600ParameterMode::DelayTime:      Serial.println("Delay Time"); break;
      case AS5600ParameterMode::DelayFeedback:  Serial.println("Delay Feedback"); break;
      case AS5600ParameterMode::LFO1freq:       Serial.println("LFO1 Frequency"); break;
      case AS5600ParameterMode::LFO1amp:        Serial.println("LFO1 Amplitude"); break;
      case AS5600ParameterMode::LFO2freq:       Serial.println("LFO2 Frequency"); break;
      case AS5600ParameterMode::LFO2amp:        Serial.println("LFO2 Amplitude"); break;
      case AS5600ParameterMode::Shuffle:        Serial.println("Shuffle Template"); break;
      case AS5600ParameterMode::COUNT:          break; // Should not happen
    }
  }
}